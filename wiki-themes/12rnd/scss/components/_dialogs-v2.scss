// ============================================================================
// Modern Dialog Styling (ph-dialog-v2)
// ============================================================================
// This file contains the updated modal styling from portal frontend
// Use the 'ph-dialog-v2' class on modal content to enable modern styling

// Main container styling when ph-dialog-v2 is present
.swal2-container:has(.ph-dialog-v2) {
    .swal2-popup {
        border-radius: 4px;
        padding: 0px;
    }
    
    // Header styling with background and proper spacing
    .swal2-header:not(:has(.swal2-title:empty)) {
        background-color: #f6f8fa;
        border-radius: 4px 4px 0px 0px;
        padding: 10px 20px 9px;
        align-items: flex-start;
        border-bottom: 1px solid #ebeef1;

        svg {
            margin-right: 12px;
        }
    }
    
    // Title styling
    .swal2-title {
        margin: 0px;
        padding: 0px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        text-align: left;
    }
    
    // Content area styling
    .swal2-content {
        text-align: start;
        
        .body {
            padding: 20px;
        }
        
        .footer {
            padding: 10px 20px 10px 20px;
            border-top: 1px solid #EBEEF1;
            margin: 0px;

            &:is(:empty) {
                display: none;
            }
        }
    }
    
    // Close button styling
    .swal2-close {
        margin: 10px 18px 0px 0px;
        font-size: 16px;
        color: #000;
        background-color: unset;
    }
    
    // Action buttons styling
    .swal2-actions {
        margin: 0px;
        gap: 8px;
        border-top: 1px solid #EBEEF1;
        padding: 10px 20px 10px 20px;
        justify-content: flex-end;
        
        button {
            margin: 0px;
        }
    }
}

// Additional ph-dialog-v2 content styling
.ph-dialog-v2 {
    &__content {
        padding: 20px;
        min-height: 100px;

        &--prompt-confirmation {
            h6 {
                font-size: 1.8rem;
            }
        }
        
        .swal2-input,
        .swal2-textarea {
            margin: 0 0 1em;
        }
    }
}

// Legacy ph-dialog support (for gradual migration)
.swal2-container:has(.ph-dialog) {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-top: 1px solid lightgrey;
        margin: 2px;
        padding: 10px;

        label {
            font-weight: 400;
        }
        
        .bootstrap-tagsinput {
            border: none !important;
        }
    }
    
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
    
    .swal2-close {
        margin: 0px !important;
    }
}

// Utility classes for modal content structure
.ph-modal-body {
    padding: 20px;
}

.ph-modal-footer {
    padding: 10px 20px;
    border-top: 1px solid #EBEEF1;
    margin: 0px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    
    &:empty {
        display: none;
    }
}

// Class-based activation for gradual migration
.use-modern-modal {
    // This class can be added to trigger elements to indicate they should use modern modals
    // Implementation will be handled in JavaScript
}
