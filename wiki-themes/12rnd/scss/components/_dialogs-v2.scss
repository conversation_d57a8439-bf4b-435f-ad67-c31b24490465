// ============================================================================
// Modern Dialog Styling (ph-dialog-v2)
// ============================================================================
// This file contains the updated modal styling from portal frontend
// Use the 'ph-dialog-v2' class on modal content to enable modern styling

// Fallback for browsers that don't support :has() selector
.swal2-container .ph-dialog-v2 {
    display: block; // This ensures the class is present in the container
}

// Main container styling when ph-dialog-v2 is present
.swal2-container:has(.ph-dialog-v2),
.swal2-container.ph-dialog-v2-container {
    div.swal2-popup {
        border-radius: 4px !important;
        padding: 0px !important;
    }

    // Header styling with background and proper spacing - higher specificity
    div.swal2-popup .swal2-header {
        background-color: #f6f8fa !important;
        border-radius: 4px 4px 0px 0px !important;
        padding: 10px 20px 9px !important;
        align-items: flex-start !important;
        border-bottom: 1px solid #ebeef1 !important;

        svg {
            margin-right: 12px;
        }
    }

    // Title styling - higher specificity to override default SweetAlert2 styles
    div.swal2-popup h2.swal2-title {
        margin: 0px !important;
        padding: 0px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        line-height: 20px !important;
        text-align: left !important;
        color: #1f2937 !important;
    }
}
    
    // Content area styling - higher specificity
    div.swal2-popup .swal2-content {
        text-align: start !important;

        .body {
            padding: 20px;
        }

        .footer {
            padding: 10px 20px 10px 20px;
            border-top: 1px solid #EBEEF1;
            margin: 0px;

            &:is(:empty) {
                display: none;
            }
        }
    }

    // Close button styling - higher specificity
    div.swal2-popup .swal2-close {
        margin: 10px 18px 0px 0px !important;
        font-size: 16px !important;
        color: #000 !important;
        background-color: unset !important;
    }

    // Action buttons styling - higher specificity
    div.swal2-popup .swal2-actions {
        margin: 0px !important;
        gap: 8px !important;
        border-top: 1px solid #EBEEF1 !important;
        padding: 10px 20px 10px 20px !important;
        justify-content: flex-end !important;

        button {
            margin: 0px !important;
        }
    }
}

// Additional ph-dialog-v2 content styling
.ph-dialog-v2 {
    &__content {
        padding: 20px;
        min-height: 100px;

        &--prompt-confirmation {
            h6 {
                font-size: 1.8rem;
            }
        }
        
        .swal2-input,
        .swal2-textarea {
            margin: 0 0 1em;
        }
    }
}

// Legacy ph-dialog support (for gradual migration)
.swal2-container:has(.ph-dialog) {
    fieldset {
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-top: 1px solid lightgrey;
        margin: 2px;
        padding: 10px;

        label {
            font-weight: 400;
        }
        
        .bootstrap-tagsinput {
            border: none !important;
        }
    }
    
    legend {
        padding-left: 5px;
        padding-right: 5px;
        font-weight: bold;
        width: auto;
        padding: inherit;
        margin-bottom: auto;
        font-size: 15px;
        line-height: 1px;
        border: none;
        border-bottom: none;
    }
    
    .swal2-close {
        margin: 0px !important;
    }
}

// Utility classes for modal content structure
.ph-modal-body {
    padding: 20px;
}

.ph-modal-footer {
    padding: 10px 20px;
    border-top: 1px solid #EBEEF1;
    margin: 0px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    
    &:empty {
        display: none;
    }
}

// Class-based activation for gradual migration
.use-modern-modal {
    cursor: pointer; // This class can be added to trigger elements to indicate they should use modern modals
}
