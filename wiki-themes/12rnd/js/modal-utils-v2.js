/**
 * Modern Modal Utilities for Wiki Frontend
 * Provides easy-to-use functions for creating modals with updated UI
 */

// Enhanced Swal mixin with modern styling
const modernSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary waves-effect',
        cancelButton: 'btn btn-default waves-effect'
    },
    buttonsStyling: false,
    animation: false,
    focusConfirm: false,
    didOpen: function () {
        window.top.postMessage("modalShow", "*");
    },
    didClose: function () {
        window.top.postMessage("modalHide", "*");
    },
    backdrop: `rgba(0,0,0,0.5)`,
    showClass: {
        backdrop: "swal2-noanimation",
        popup: "",
        icon: "",
    },
    hideClass: {
        popup: "",
    },
});

/**
 * Creates a modern dialog with ph-dialog-v2 styling
 * @param {Object} options - Dialog configuration
 * @param {string} options.title - Dialog title
 * @param {string} options.bodyContent - HTML content for dialog body
 * @param {string} options.footerContent - HTML content for dialog footer (optional)
 * @param {Object} options.swalOptions - Additional Swal options (optional)
 * @returns {Promise} Swal promise
 */
function createModernDialog(options) {
    const {
        title,
        bodyContent,
        footerContent = '',
        swalOptions = {}
    } = options;

    // Create the ph-dialog-v2 wrapper
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'ph-dialog-v2';
    
    // Structure the content
    let dialogHTML = `<div class="body">${bodyContent}</div>`;
    if (footerContent) {
        dialogHTML += `<div class="footer">${footerContent}</div>`;
    }
    
    dialogContainer.innerHTML = dialogHTML;

    // Default options
    const defaultOptions = {
        title: title || 'Dialog',
        html: dialogContainer,
        width: 500,
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: false,
        allowOutsideClick: true,
        allowEscapeKey: true,
    };

    // Merge with custom options
    const finalOptions = { ...defaultOptions, ...swalOptions };

    return modernSwal.fire(finalOptions);
}

/**
 * Creates a modern confirmation dialog
 * @param {Object} options - Confirmation dialog configuration
 * @param {string} options.title - Dialog title
 * @param {string} options.message - Confirmation message
 * @param {Function} options.onConfirm - Callback for confirm action
 * @param {Function} options.onCancel - Callback for cancel action (optional)
 * @param {Object} options.swalOptions - Additional Swal options (optional)
 * @returns {Promise} Swal promise
 */
function createModernConfirmDialog(options) {
    const {
        title,
        message,
        onConfirm,
        onCancel = () => null,
        swalOptions = {}
    } = options;

    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'ph-dialog-v2';
    dialogContainer.innerHTML = `<div class="body">${message || t('Are you sure you want to proceed?')}</div>`;

    const defaultOptions = {
        title: title || t('Confirm'),
        html: dialogContainer,
        width: 500,
        showCancelButton: true,
        confirmButtonText: t('Confirm'),
        cancelButtonText: t('Cancel'),
    };

    const finalOptions = { ...defaultOptions, ...swalOptions };

    return modernSwal.fire(finalOptions).then(response => {
        response.value ? onConfirm() : onCancel();
    });
}

/**
 * Creates a modern delete confirmation dialog
 * @param {Object} options - Delete confirmation dialog configuration
 * @param {string} options.title - Dialog title
 * @param {string} options.message - Delete confirmation message
 * @param {Function} options.onConfirm - Callback for confirm action
 * @param {string} options.confirmText - Confirm button text (default: 'Delete')
 * @param {Object} options.swalOptions - Additional Swal options (optional)
 * @returns {Promise} Swal promise
 */
function createModernDeleteDialog(options) {
    const {
        title,
        message,
        onConfirm,
        confirmText = 'Delete',
        swalOptions = {}
    } = options;

    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'ph-dialog-v2';
    dialogContainer.innerHTML = `<div class="body">${message || t('Are you sure you want to delete this item?')}</div>`;

    const defaultOptions = {
        title: title || t('Confirm Delete'),
        html: dialogContainer,
        width: 500,
        showCancelButton: true,
        confirmButtonText: t(confirmText),
        cancelButtonText: t('Cancel'),
        allowOutsideClick: false,
        allowEscapeKey: false,
        customClass: {
            confirmButton: 'btn btn-danger waves-effect',
            cancelButton: 'btn btn-default waves-effect'
        }
    };

    const finalOptions = { ...defaultOptions, ...swalOptions };

    return modernSwal.fire(finalOptions).then(response => {
        if (response.value) {
            onConfirm();
        }
    });
}

/**
 * Creates a modern information dialog
 * @param {Object} options - Information dialog configuration
 * @param {string} options.title - Dialog title
 * @param {string} options.message - Information message
 * @param {Object} options.swalOptions - Additional Swal options (optional)
 * @returns {Promise} Swal promise
 */
function createModernInfoDialog(options) {
    const {
        title,
        message,
        swalOptions = {}
    } = options;

    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'ph-dialog-v2';
    dialogContainer.innerHTML = `<div class="body">${message || t('Details not provided.')}</div>`;

    const defaultOptions = {
        title: title || t('Information'),
        html: dialogContainer,
        width: 500,
        confirmButtonText: t('OK'),
        showCancelButton: false,
        allowOutsideClick: true,
        allowEscapeKey: true,
    };

    const finalOptions = { ...defaultOptions, ...swalOptions };

    return modernSwal.fire(finalOptions);
}

/**
 * Utility function to wrap existing HTML content with ph-dialog-v2 structure
 * @param {string} htmlContent - Existing HTML content
 * @param {string} footerContent - Optional footer content
 * @returns {HTMLElement} Wrapped dialog element
 */
function wrapWithModernDialog(htmlContent, footerContent = '') {
    const dialogContainer = document.createElement('div');
    dialogContainer.className = 'ph-dialog-v2';
    
    let dialogHTML = `<div class="body">${htmlContent}</div>`;
    if (footerContent) {
        dialogHTML += `<div class="footer">${footerContent}</div>`;
    }
    
    dialogContainer.innerHTML = dialogHTML;
    return dialogContainer;
}

// Export functions for use in other files
if (typeof window !== 'undefined') {
    window.modernModalUtils = {
        createModernDialog,
        createModernConfirmDialog,
        createModernDeleteDialog,
        createModernInfoDialog,
        wrapWithModernDialog,
        modernSwal
    };
}
