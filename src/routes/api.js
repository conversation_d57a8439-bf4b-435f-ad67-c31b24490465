const express = require('express');
const basicAuth = require('express-basic-auth');
const router = express.Router();
const common = require('./common');
const Validator = require('jsonschema').Validator;
const config = common.read_config();
const { getArticlesByTopicId } = require('./services/getArticlesByTopicId');
const { getFeaturedArticlesByTopicId } = require('./services/getFeaturedArticlesByTopic');
const { generateStructuredOutput } = require('../utils/openaiService');

const stripHtml = require("string-strip-html");
const { getDb } = require('../lib/db');
const { syncArticlesToAlgolia } = require('../lib/algolia');

const BASIC_USER = process.env.BASIC_USER || 'algolia'
const BASIC_PASS = process.env.BASIC_PASS || 'wmZ8i3jYERUtzoF';

// Setup basic auth
const basicAuthMiddleware = basicAuth({
    users: { [BASIC_USER]: BASIC_PASS },
    challenge: true
});

// validate the permalink
router.post('/api/getArticleJson', async (req, res) => {
    const db = await getDb();

    db.kb.findOne({ _id: common.getId(req.body.kb_id) }, (err, result) => {
        if(err){
            res.status(400).json({ message: 'Article not found' });
        }else{
            res.status(200).json(result);
        }
    });
});

router.get('/api/articles/:id/duplicate', async (req, res) => {
    const db = await getDb();
    const countries = await common.getCountries();

    //include the countries in the result
    
    const countriesOnly = [];

    if(req.session.is_pseudoadmin !== true) {
        countries.forEach((c) => {
            countriesOnly.push(c.full_name);
        });
    } else {
        countries.forEach((c) => {
            if(req.session.user_regions.findIndex(r => {
                return r.country === c.full_name
            }) !== -1) {
                countriesOnly.push(c.full_name);
            }
        });
    }

    const article = await db.kb.findOne({ _id: common.getId(req.params.id) });    
    article.countries = countriesOnly;

    res.status(200).json(article);
});

router.post('/api/articles/generate-slug', async (req, res) => {
    const db = await getDb();
    const title = req.body.title;
    const id = req.body.id;
    
    const generatedSlug = await common.generateSlug(db, title, id);
    
    res.status(200).json({slug: generatedSlug});
})

const wrapSkipTranslate = (text) => {
    const listOfTexts = [
        '```',
        '$alert',
        '$accordion-start',
        '$accordion-search-start',
        '$placeholder:',
        '$accordion-search-end',
        '$panel-start',
        '$title:',
        '$body:',
        '$panel-end',
        '$accordion-end',
        '$container-start',
        '$item:',
        '$height:',
        '$width:',
        '$align:left',
        '$align:right',
        '$align:center',
        '$container-end',
        
    ]
    listOfTexts.forEach((t) => {
        const regExpr = new RegExp('\\'+t, "gm");
    
        text = text.replace(regExpr, `<span class="notranslate">${t}</span>`);
    })

    const regExpr2 = new RegExp('[\r\n]+', "gm");

    text = text.replace(regExpr2, '<br />');
    
    return text;
}

//unwrap part of string that are wrapped inside <span class="skiptranslate"> and </span>
const unwrapSkipTranslate = (text) => {
    const regExpr = new RegExp('<span class="notranslate">(.*)</span>', "gm");
    text = text.replace(regExpr, '$1')
    //replace all <br /> with \n
    const regExpr2 = new RegExp('<br />', "gm");
    text = text.replace(regExpr2, '\n');
    text = text.replace(/^\s+|\s+$/gm, '')
    return text;
}

const cleanTranslatedBody = async(text, lang) => {
    const skipWrappedBody = wrapSkipTranslate(text);
    
    const translatedBody = await common.getTranslation(skipWrappedBody, lang);
    
    return unwrapSkipTranslate(translatedBody[lang] );
}


router.post('/api/articles/:id/duplicate', async (req, res) => {
    const db = await getDb();

    db.kb.findOne({ _id: common.getId(req.params.id), kb_versioned_doc: { $ne: true } }, async(err, result) => {
        if (!result) {
            res.render("error", {
                message: "404 - Page not found",
                helpers: req.handlebars,
                config: config,
            });
            return;
        }

        const title = req.body.article_dup_do_translate === 'on' ? await common.getTranslation(req.body.kb_dup_title, req.body.kb_dup_lang) : null;
        const body = req.body.article_dup_do_translate ===  'on' ?  await cleanTranslatedBody(result.kb_body, req.body.kb_dup_lang): result.kb_body;
        const seoTitle = req.body.article_dup_do_translate === 'on' ?  await common.getTranslation(result.kb_seo_title ?? '', req.body.kb_dup_lang) : null;
        const seoDesc = req.body.article_dup_do_translate === 'on' ? await common.getTranslation(result.kb_seo_description ?? '', req.body.kb_dup_lang) : null;

        const permalink = await common.generateSlug(db, title ? title[req.body.kb_dup_lang] : req.body.kb_dup_title);
        
        const keywords = result.kb_keywords.split(/(?:,)\s*/i);
        const translatedKeywords = [];
       
        if( req.body.article_dup_do_translate === 'on') {
            //translate keywords if needed
            for(const keyword of keywords){
                if(keyword === '') continue;
                const res = await common.getTranslation(keyword, req.body.kb_dup_lang);
                
                const translatedKeyword = res[req.body.kb_dup_lang];
                translatedKeywords.push(translatedKeyword);
            }
        } else {
            for(const keyword of keywords){
                translatedKeywords.push(keyword);
            }
        }
        

        let doc = {
            kb_permalink: permalink,
            kb_title: title ? title[req.body.kb_dup_lang] : req.body.kb_dup_title,
            kb_body: body ?? '',
            kb_published: false,
            kb_keywords: translatedKeywords.join(', '),
            kb_published_date: new Date(),
            kb_last_updated: new Date(),
            kb_last_update_user: req.session.users_name + " - " + req.session.user,
            kb_author: req.session.users_name,
            kb_author_email: req.session.user,
            kb_security_level: 100,
            kb_regional_visibility: req.body.kb_dup_region,
            kb_language: req.body.kb_dup_lang,
            kb_pinned_subtopic: result.kb_pinned_subtopic,
            kb_pinned_topic: result.kb_pinned_topic,
            kb_translation_title: await common.translateTitle(req, req.body.kb_dup_title),

            kb_seo_title: seoTitle ? seoTitle[req.body.kb_dup_lang] : result.kb_seo_title,
            kb_seo_description: seoDesc ? seoDesc[req.body.kb_dup_lang] : result.kb_seo_description,
            kb_visible_state: result.kb_visible_state,
            kb_pinned: result.kb_pinned,
        };

        db.kb.count({ kb_permalink: permalink }, (err, kb) => {
            if (kb > 0 && req.body.kb_dup_lang+'/'+result.kb_permalink !== "") {
                // permalink exits
                req.session.message = req.i18n.__("Permalink already exists. Pick a new one.");
                req.session.message_type = "danger";
    
                // keep the current stuff
                // req.session.kb_title = do;
                // req.session.kb_body = result.kb_body;
                // req.session.kb_keywords = result.kb_keywords;
                // req.session.kb_permalink = req.body.frm_kb_permalink;
    
                // redirect to insert
                res.redirect(req.app_context + "/articles");
            } else {
                //inserts actual article
                
                db.kb.insert(doc, (err, newDoc) => {
                    if (err) {
                        console.error("Error inserting document: " + err);
    
                        // keep the current stuff
                        req.session.kb_title = doc.title;
                        req.session.kb_body = doc.kb_body;
                        req.session.kb_keywords = doc.kb_keywords;
                        req.session.kb_permalink = doc.kb_permalink;
    
                        req.session.message = req.i18n.__("Error") + ": " + err;
                        req.session.message_type = "danger";
    
                        // redirect to insert
                        res.redirect(req.app_context + "/articles");
                    } else {
                        console.log('INSERTED NEW DOC', newDoc);
                        let newId = newDoc.insertedIds["0"].toString();
    
                        //also insert versioned
                        //recycle doc
                        doc.kb_versioned_doc = true;
                        doc.kb_password = "";
                        doc.kb_published = false;
                        doc.kb_edit_reason = "Article Duplicated";
                        doc.kb_versioned_doc = true;
                        doc.kb_parent_id = newId;
                        
                        delete doc._id;
    
                        // insert a doc to track versioning
                        db.kb.insert(doc, (err, version_doc) => {
                            if (err) {
                                console.log("Error while inserting versioned doc", err);
                            } else {
                                console.log("Inserted versioned doc as Initial Article history.", version_doc);
                            }
                        });

                        syncArticlesToAlgolia();

                        // redirect to new doc
                        if(err){
                            res.status(400).json({ message: 'Error in duplicating article' });
                        }else{
                            res.status(200).json({status: "success", data: {id: newId}});
                        }
                    }
                });
            }
        });
    });
});

// validate the permalink
router.post('/api/deleteVersion', async (req, res) => {
    const db = await getDb();

    // only allow admin
    if(req.session.is_admin !== 'true'){
        res.status(400).json({ message: 'Admin access required' });
        return;
    }

    db.kb.remove({ _id: common.getId(req.body.kb_id) }, {}, (err, numRemoved) => {
        if(err){
            res.status(400).json({ message: 'Article not found' });
        }else{
            res.status(200).json({});
        }
    });
});

// validate the permalink
router.post('/api/validate_permalink', async (req, res) => {
    const db = await getDb();

    // if doc id is provided it checks for permalink in any docs other that one provided,
    // else it just checks for any kb's with that permalink
    let query = {};
    if(req.body.doc_id === ''){
        query = { 'kb_permalink': req.body.permalink };
    }
    query = { 'kb_permalink': req.body.permalink};

    if(req.body.doc_id !== '' && req.body.doc_id !== undefined){
        query._id = { $ne: common.getId(req.body.doc_id) } 
    }

    console.log('query',query)

    db.kb.count(query, (err, kb) => {
        if(kb > 0){
            res.writeHead(400, { 'Content-Type': 'application/text' });
            res.end('Permalink already exists');
        }else{
            res.writeHead(200, { 'Content-Type': 'application/text' });
            res.end('Permalink validated successfully');
        }
    });
});

// public API for inserting posts
router.post('/api/newArticle', async (req, res) => {
    const db = await getDb();
    const v = new Validator();

    // if API is not set or set to false we stop it
    if(typeof config.settings.api_allowed === 'undefined' || config.settings.api_allowed === false){
        res.status(400).json({ result: false, errors: ['Not allowed'] });
        return;
    }

    // if API token is not set or set to an empty value we stop it. Accidently allowing a public API with no token is no 'toke'.
    if(typeof config.settings.api_auth_token === 'undefined' || config.settings.api_auth_token === ''){
        res.status(400).json({ result: false, errors: ['Not allowed'] });
        return;
    }

    // The API schema
    let articleSchema = {
        'type': 'object',
        'properties': {
            'api_auth_token': { 'type': 'string' },
            'kb_title': { 'type': 'string' },
            'kb_body': { 'type': 'string' },
            'kb_permalink': { 'type': 'string' },
            'kb_published': { 'type': 'boolean' },
            'kb_keywords': { 'type': 'string' },
            'kb_author_email': { 'type': 'string' },
            'kb_password': { 'type': 'string' },
            'kb_featured': { 'type': 'boolean' },
            'kb_seo_title': { 'type': 'string' },
            'kb_seo_description': { 'type': 'string' }
        },
        'required': ['api_auth_token', 'kb_title', 'kb_body', 'kb_author_email', 'kb_published']
    };

    // validate against schema
    let validation = v.validate(req.body, articleSchema);
    let validationResult = validation.errors.length !== 1;

    // if have some data
    if(req.body){
        // check auth token is correct
        if(req.body.api_auth_token && req.body.api_auth_token === config.settings.api_auth_token){
            // token is ok and validated, we insert into DB

            // check permalink if it exists
            common.validate_permalink(db, req.body, (err, result) => {
                // duplicate permalink
                if(err){
                    res.status(400).json({ result: false, errors: [err] });
                    return;
                }

                // check all required data is present and correct
                if(validationResult === true){
                    // find the user by email supplied
                    db.users.findOne({ user_email: req.body.kb_author_email }, (err, user) => {
                        // if error or user not found
                        if(err || user === null){
                            res.status(400).json({ result: false, errors: ['No author found with supplied email'] });
                            return;
                        }

                        let featuredArticle = typeof req.body.kb_featured !== 'undefined' ? req.body.kb_featured.toString() : 'false';
                        let publishedArticle = typeof req.body.kb_published !== 'undefined' ? req.body.kb_published.toString() : 'false';

                        // setup the doc to insert
                        let doc = {
                            kb_permalink: req.body.kb_permalink,
                            kb_title: req.body.kb_title,
                            kb_body: req.body.kb_body,
                            kb_published: publishedArticle,
                            kb_keywords: req.body.kb_keywords,
                            kb_published_date: new Date(),
                            kb_last_updated: new Date(),
                            kb_featured: featuredArticle,
                            kb_last_update_user: user.users_name + ' - ' + user.user_email,
                            kb_author: user.users_name,
                            kb_author_email: user.user_email,
                            kb_seo_title: req.body.kb_seo_title,
                            kb_seo_description: req.body.kb_seo_description,
                        };

                        // insert article
                        db.kb.insert(doc, (err, newDoc) => {
                            if(err){
                                res.status(400).json({ result: false, errors: [err] });
                                return;
                            }

                            syncArticlesToAlgolia();
                            res.status(200).json({ result: true, message: 'All good' });
                        });
                    });
                }else{
                    res.status(400).json({ result: false, errors: [validation.errors] });
                }
            });
        }else{
            res.status(400).json({ result: false, errors: ['Incorrect or invalid auth token'] });
        }
    }else{
        res.status(400).json({ result: false, errors: ['No data'] });
    }
});

// Returns all pinned topics
router.get('/api/pinned_topics', async (req, res) => {
    const db = await getDb();

    common.dbQuery(db.kb, { kb_published: 'true', kb_pinned: 'true' }, null, null, (err, topics) => {
        if (err) {
            console.error(err);
            return res.status(500).json({ message: 'Something went wrong.' })
        }

        res.status(200).json({ data: topics });
    });
});

router.get('/api/all_published', basicAuthMiddleware, async (req, res) => {
    const db = await getDb();

    // Set default values for pagination
    const limit = parseInt(req.query.limit) || 10000; // By default - let's return 10,000 records.
    const page = parseInt(req.query.page) || 1; // Page number

    const skip = (page - 1) * limit; // Calculate the number of records to skip

    common.dbQuery(db.kb, { kb_published: 'true' }, { limit: limit, skip: skip }, null, async(err, articles) => {
        if (err) {
            console.error(err);
            return res.status(500).json({ message: 'Something went wrong.' })
        }

        const transformedArticles = []
        for(const article of articles) {
            if (!article.kb_regional_visibility || article.kb_regional_visibility === '') {
                article.kb_regional_visibility = ['global'];
            } else {
                article.kb_regional_visibility = article.kb_regional_visibility ? article.kb_regional_visibility.split(',').map(region => region.trim()) : [];
            }

            const topic = await db.topics.findOne({ _id: article.kb_pinned_topic });           
    
            article.topic = topic;
            
            if(topic && topic.subtopics) {
                article.subtopic = topic.subtopics ? topic.subtopics.find(el => el.id == article.kb_pinned_subtopic) : ''
            }
        
            transformedArticles.push(article);
        }


        res.status(200).json(transformedArticles);
    });
});



router.get("/api/topics", common.restrict, async (req, res) => {
    const db = await getDb();

    if(req.session.is_pseudoadmin) {

        const topicQuery = [{ topic_regional_visibility: "" }];
        req.session.user_regions.forEach(r => {
            const regExpr = new RegExp(".*" + r.country + ".*", "i");
            topicQuery.push({ topic_regional_visibility: { $regex: regExpr } });
        })
        console.log('topicquery',topicQuery)
            
            common.dbQuery(db.topics, {
                $or: topicQuery,
                enabled: "true",
            }, { display_order: 1 }, null,

                // db.topics
                //     .find({
                //         $or: [{ topic_regional_visibility: "" }, { topic_regional_visibility: { $regex: regExpr } }],
                //         enabled: "true",
                //     })
                //     .sort({ display_order: 1 })
                // 	.exec(
                function (err, topics) {
                    if (err) {
                        console.log(err);
                        return res.status(400).json({ message: "fail" });
                    } else {
                        return res.status(200).json({ message: "success", data: topics });
                    }
                });
    } else {
        common.dbQuery(db.topics, { enabled: "true" }, { display_order: 1 }, null,

        // db.topics
        //     .find({ topic_regional_visibility: "", enabled: "true" })
        //     .sort({ display_order: 1 })
        // 	.exec(
        function (err, topics) {
            if (err) {
                return res.status(400).json({ message: "fail" });
            } else {
                return res.status(200).json({ message: "success", data: topics });
            }
        });
    }
});

router.post("/api/insert_kb", common.restrict, async (req, res) => {
    const db = await getDb();
    const kb_is_markdown = req.body.frm_kb_is_markdown === "on" ? true : false
                    
    const kb_body = kb_is_markdown ? req.body.frm_kb_body : common.convertBodyToHtml(req.body.frm_kb_body)

    let doc = {
        kb_permalink: req.body.frm_kb_permalink,
        kb_title: req.body.frm_kb_title,
        kb_body: kb_body,
        kb_published: req.body.frm_kb_published,
        kb_keywords: req.body.frm_kb_keywords,
        kb_published_date: new Date(),
        kb_last_updated: new Date(),
        kb_last_update_user: req.session.users_name + " - " + req.session.user,
        kb_author: req.session.users_name,
        kb_author_email: req.session.user,
        kb_password: req.body.frm_kb_password,
        kb_featured: req.body.frm_kb_featured === "on" ? "true" : "false",
        kb_pinned:  req.body.frm_kb_pinned === "on" ? "true" : "false",
        
        kb_pinned_topic:  req.body.frm_kb_pinned === 'on' ? parseInt(req.body.frm_kb_pinned_topic) : '',
        kb_visible_state: req.body.frm_kb_visible_state,
        kb_security_level: parseInt(req.body.frm_kb_security_level),
		kb_regional_visibility: req.body.frm_kb_regional_visibility,
		kb_pinned_subtopic:  req.body.frm_kb_pinned === 'on' ? parseInt(req.body.frm_kb_pinned_subtopic) : '',
        kb_translation_title : await common.translateTitle(req, req.body.frm_kb_title),
        kb_language:  req.body.frm_kb_language ?? 'en',
        kb_admin_restricted: req.body.frm_kb_admin_restricted === "on" ? "true" : "false",
        kb_is_markdown: kb_is_markdown
    };

	
    db.kb.count({ kb_permalink: req.body.frm_kb_permalink }, (err, kb) => {
        if (kb > 0 && req.body.frm_kb_permalink !== "") {
            // permalink exits
            return res.status(400).json({ message: "Permalink already exists. Pick a new one.", status: "error" });
		} else {
            //inserts actual article
			
            db.kb.insert(doc, (err, newDoc) => {
				if (err) {
                    return res.status(400).json({ message: "Something went wrong. Please try again later", status: "error" });
				} else {
					console.log('INSERTED NEW DOC', newDoc);
                    let newId = newDoc.insertedIds["0"].toString();

                    //also insert versioned
                    //recycle doc
                    doc.kb_versioned_doc = true;
                    doc.kb_password = "";
                    doc.kb_published = false;
                    doc.kb_edit_reason = "Initial Article Created";
                    doc.kb_versioned_doc = true;
					doc.kb_parent_id = newId;
					
					delete doc._id;

                    // insert a doc to track versioning
                    db.kb.insert(doc, (err, version_doc) => {
                        if (err) {
                            console.log("Error while inserting versioned doc", err);
                        } else {
                            console.log("Inserted versioned doc as Initial Article history.", version_doc);
                        }
                    });

                    syncArticlesToAlgolia();
                    return res.status(200).json({ message: "New Article Successfully created", status: "success", data: {id: newId} });
					
                }
			});


        }
    });
});


router.post("/api/save_kb", common.restrict, async (req, res) => {
    const db = await getDb();

    let kb_featured = req.body.frm_kb_featured === "on" ? "true" : "false";
    let kb_pinned = req.body.frm_kb_pinned === "on" ? "true" : "false";
    let kb_dashboard = req.body.frm_kb_dashboard === "on" ? "true" : "false";

    // if empty, remove the comma and just have a blank string
    let keywords = req.body.frm_kb_keywords.replace(/<(?:.|\n)*?>/gm, "");
    if (common.safe_trim(keywords) === ",") {
        keywords = "";
    }

    db.kb.count(
        {
            kb_permalink: req.body.frm_kb_permalink,
            _id: { $ne: common.getId(req.body.frm_kb_id) },
            kb_versioned_doc: { $ne: true },
        },
        (err, kb) => {
        if(req.session.is_pseudoadmin && kb.kb_admin_restricted === "true") {
                return res.status(403).json({ message: "User not permitted to update this article", status: "error" });
            }

            if (kb > 0 && req.body.frm_kb_permalink !== "") {
                // permalink exits
                return res.status(400).json({ message: "Permalink already exists. Pick a new one.", status: "error" });
          
            } else {
				db.kb.findOne({ _id: common.getId(req.body.frm_kb_id) }, async(err, article) => {
					
					console.log('FOUND ID', article._id);
					
                    // update author if not set
                    let author = article.kb_author ? article.kb_author : req.session.users_name;
                    let author_email = article.kb_author_email ? article.kb_author_email : req.session.user;

                    // set published date to now if none exists
                    let published_date;
                    // if (article.kb_published_date == null || article.kb_published_date === undefined) {
                    //     published_date = new Date();
                    // } else {
                    //     published_date = article.kb_published_date;
					// }

					//update kb_published date when there's no published date and article is tagged as published
					if (JSON.parse(req.body.frm_kb_published) && (article.kb_published_date == null || article.kb_published_date === undefined)) {
						published_date = new Date();
					} else {
						//for older articles - it might have a value that is not a valid date
						if (isNaN(Date.parse(article.kb_published_date))) {
							published_date = new Date();
						} else {
							published_date = article.kb_published_date;
						}
					}

					console.log('Published Date: ', published_date);

                    const titleTranslation = await common.translateTitle(req, req.body.frm_kb_title);

                    const kb_is_markdown = req.body.frm_kb_is_markdown === "on" ? true : false
                    
                    let kb_body = ''
                    if(kb_is_markdown && article.kb_is_markdown === false) {
                        kb_body = common.convertBodyToMarkdown(req.body.frm_kb_body);
                    } else if (!kb_is_markdown && article.kb_is_markdown === true) {
                        kb_body = common.convertBodyToHtml(req.body.frm_kb_body)
                    } else {
                        kb_body = req.body.frm_kb_body;
                    }
 
                 
					let updateObject = {
						kb_title: req.body.frm_kb_title,
						kb_body: kb_body,
						kb_published: req.body.frm_kb_published,
						kb_keywords: keywords,
						kb_last_updated: new Date(),
						kb_last_update_user: req.session.users_name + " - " + req.session.user,
						kb_author: author,
						kb_author_email: author_email,
						kb_published_date: published_date,
						kb_password: req.body.frm_kb_password,
						kb_permalink: req.body.frm_kb_permalink,
						kb_featured: kb_featured,
						kb_pinned: kb_pinned,
						kb_dashboard: kb_dashboard,
						kb_pinned_topic:  kb_pinned === 'true' ? parseInt(req.body.frm_kb_pinned_topic) : '',
						kb_seo_title: req.body.frm_kb_seo_title,
						kb_seo_description: req.body.frm_kb_seo_description,
						kb_visible_state: req.body.frm_kb_visible_state,
						kb_security_level: parseInt(req.body.frm_kb_security_level),
						kb_regional_visibility: req.body.frm_kb_regional_visibility,
						kb_pinned_subtopic:  kb_pinned === 'true' ? parseInt(req.body.frm_kb_pinned_subtopic) : '',
                        kb_translation_title : titleTranslation,
                        kb_language:  req.body.frm_kb_language ?? 'en',
                        kb_admin_restricted: req.body.frm_kb_admin_restricted === "on" ? "true" : "false",
                        kb_is_markdown: kb_is_markdown
					}

                  
                    // update our old doc
                    db.kb.update(
                        { _id: common.getId(req.body.frm_kb_id) },
                        { $set: updateObject },
                        {},
                        async(err, numReplaced) => {
							
                            if (err) {
                                return res.status(400).json({ message: "Failed to save. Please try again", status: "error" });
                            } else {
                                //if (article_versioning === true) {
                                    // version doc
                                    let version_doc = {
                                        kb_title: req.body.frm_kb_title,
                                        // kb_parent_id: req.body.frm_kb_id,
                                        kb_parent_id: article._id.toString(),
                                        kb_versioned_doc: true,
                                        kb_edit_reason: req.body.frm_kb_edit_reason,
                                        kb_body: req.body.frm_kb_body,
                                        kb_published: false,
                                        kb_keywords: keywords,
                                        kb_last_updated: new Date(),
                                        kb_last_update_user: req.session.users_name + " - " + req.session.user,
                                        kb_author: author,
                                        kb_author_email: author_email,
                                        kb_published_date: published_date,
                                        kb_password: req.body.frm_kb_password,
                                        kb_permalink: req.body.frm_kb_permalink,
                                        kb_featured: kb_featured,
                                        kb_pinned: kb_pinned,
                                        kb_dashboard: kb_dashboard,
                                        kb_pinned_topic:  kb_pinned === 'true' ? req.body.kb_pinned_topic : '',
                                        kb_seo_title: req.body.frm_kb_seo_title,
                                        kb_seo_description: req.body.frm_kb_seo_description,
                                        kb_visible_state: req.body.frm_kb_visible_state,
                                        kb_security_level: parseInt(req.body.frm_kb_security_level),
                                        kb_regional_visibility: req.body.frm_kb_regional_visibility,
                                        kb_pinned_subtopic:  kb_pinned === 'true' ? req.body.frm_kb_pinned_subtopic : '',
                                        kb_is_markdown: article.kb_is_markdown
                                    };

                                    // insert a doc to track versioning
                                  db.kb.insert(version_doc, (err, version_doc) => {
                                        return res.status(200).json({ message: "Successfully Saved", status: "success" });
                                    });
                                
                                syncArticlesToAlgolia();
                            }
                        }
                    );
                });
            }
        }
    );
});


router.get('/api/topic/:topicId/articles/:subtopicId?', common.restrict, async (req, res) => {
    const db = await getDb();

    common.config_expose(req.app);
    const topicId = req.params.topicId;
    const subTopicId = req.params.subtopicId;

    //the selected club id in perfhub
    //let search_club = req.params.club;
    const search_club = req.session.clubId;

    const featuredCount = config.settings.featured_articles_count ? parseInt(config.settings.featured_articles_count) : 4;

    if (!search_club || search_club === "") {
        return res.status(400).json({ message: "Search Club is not set. Please set the club in your profile.", status: "error" });
    }
 
    //check if there are clubs
    const clubCountry = await common.getCountryOfClub(search_club);

    try {
        
        const featured_results = await getFeaturedArticlesByTopicId(topicId, subTopicId, clubCountry, featuredCount, db)
        const articles = await getArticlesByTopicId(topicId, subTopicId, clubCountry,  db)
        
        articles.map(article => {
            article.kb_body = stripHtml(article.kb_body).substring(0,350);

            return article;
        })

        return res.status(200).json({ status: "success", articles: articles, featured_articles: featured_results });
    } catch (err) {
        console.log(err)
        return res.status(400).json({ message: "Something went wrong.", status: "error" });
    }
});

router.post('/api/articles/details/mini', common.restrict, async (req, res) => {
    const ids = req.body.ids;
    common.config_expose(req.app);

    try {
        const db = await getDb();
        const articles = await db.kb.find({_id: {$in: ids}}, {projection : {
            _id: 1,
            kb_title: 1,
            kb_permalink: 1
        }}).toArray();
    
        return res.status(200).json({ status: "success", articles: articles});
    } catch (err) {
        console.log(err)
        return res.status(400).json({ message: "Something went wrong.", status: "error" });
    }
});

// OpenAI API endpoint for changelog summarization
router.post('/api/ai/summarize-changelog', common.restrict, async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        // Handle both single change and multiple changes using unified function
        if (req.body.changes && Array.isArray(req.body.changes)) {
            // Multiple changes format
            if (req.body.changes.length === 0) {
                return res.status(400).json({ error: 'Changes array cannot be empty' });
            }

            const result = await generateStructuredOutput(req.body.changes);
            return res.status(200).json({ 
                data: result,
                changeCount: req.body.changes.length,
                type: 'multiple_changes'
            });

        } else if (req.body.oldText && req.body.newText) {
            // Single change format (backward compatibility)
            if (typeof req.body.oldText !== 'string' || typeof req.body.newText !== 'string') {
                return res.status(400).json({ error: 'Invalid oldText or newText. Must provide non-empty strings.' });
            }

            const result = await generateStructuredOutput(req.body.oldText, req.body.newText);
            return res.status(200).json({ data: result });

        } else {
            return res.status(400).json({ 
                error: 'Must provide either "changes" array or "oldText"/"newText" strings' 
            });
        }

    } catch (error) {
        console.error('Error generating structured output:', error);
        return res.status(500).json({ error: error.message || 'Failed to generate structured output' });
    }
});

// Get feedback statistics for an article
router.get('/api/article/:id/feedback-stats', async (req, res) => {
    try {
        const db = await getDb();
        const articleId = req.params.id;
        const processedId = common.getId(articleId);
        
        if (!articleId || !processedId) {
            return res.status(400).json({ error: 'Invalid article ID' });
        }

        // Get the article to check if it exists
        const article = await new Promise((resolve, reject) => {
            db.kb.findOne({ _id: processedId }, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            });
        });

        if (!article) {
            return res.status(404).json({ error: 'Article not found' });
        }

        // Use count() to get actual vote counts
        const upvotes = await new Promise((resolve, reject) => {
            db.helpfulornot.count({ articleId: articleId, helpful: "true" }, (err, count) => {
                if (err) {
                    console.error('Error counting helpful votes:', err);
                    resolve(0);
                } else {
                    resolve(count || 0);
                }
            });
        });
        
        const downvotes = await new Promise((resolve, reject) => {
            db.helpfulornot.count({ articleId: articleId, helpful: "false" }, (err, count) => {
                if (err) {
                    console.error('Error counting not helpful votes:', err);
                    resolve(0);
                } else {
                    resolve(count || 0);
                }
            });
        });
        
        const stats = {
            upvotes: upvotes,
            downvotes: downvotes
        };

        return res.status(200).json({ 
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('Feedback stats API error:', error);
        return res.status(500).json({ error: 'Failed to get feedback statistics' });
    }
});

module.exports = router;
