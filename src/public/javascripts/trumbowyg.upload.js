/* ===========================================================
 * trumbowyg.upload.js v1.2
 * Upload plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON> (Alex-D)
 *          Twitter : @AlexandreDemode
 *          Website : alex-d.fr
 * Mod by : <PERSON>-ru
 *          Twitter : @Aleksandr_ru
 *          Website : aleksandr.ru
 */

(function ($) {
  'use strict';

  var defaultOptions = {
    serverPath: '',
    fileFieldName: 'fileToUpload',
    data: [],                       // Additional data for ajax [{name: 'key', value: 'value'}]
    headers: {},                    // Additional headers
    xhrFields: {},                  // Additional fields
    urlPropertyName: 'file',        // How to get url from the json response (for instance 'url' for {url: ....})
    statusPropertyName: 'success',  // How to get status from the json response
    success: undefined,             // Success callback: function (data, trumbowyg, $modal, values) {}
    error: undefined,               // Error callback: function () {}
    imageWidthModalEdit: false      // Add ability to edit image width
  };

  function getDeep(object, propertyParts) {
    var mainProperty = propertyParts.shift(),
      otherProperties = propertyParts;

    if (object !== null) {
      if (otherProperties.length === 0) {
        return object[mainProperty];
      }

      if (typeof object === 'object') {
        return getDeep(object[mainProperty], otherProperties);
      }
    }
    return object;
  }

  addXhrProgressEvent();

  $.extend(true, $.trumbowyg, {
    langs: {
      // jshint camelcase:false
      en: {
        upload: 'Upload',
        file: 'File',
        uploadError: 'Error'
      },
      az: {
        upload: 'Yüklə',
        file: 'Fayl',
        uploadError: 'Xəta'
      },
      by: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Памылка'
      },
      ca: {
        upload: 'Pujar fitxer',
        file: 'Fitxer',
        uploadError: 'Error'
      },
      cs: {
        upload: 'Nahrát obrázek',
        file: 'Soubor',
        uploadError: 'Chyba'
      },
      da: {
        upload: 'Upload',
        file: 'Fil',
        uploadError: 'Fejl'
      },
      de: {
        upload: 'Hochladen',
        file: 'Datei',
        uploadError: 'Fehler'
      },
      es: {
        upload: 'Subir archivo',
        file: 'Archivo',
        uploadError: 'Error'
      },
      et: {
        upload: 'Lae üles',
        file: 'Fail',
        uploadError: 'Viga'
      },
      fr: {
        upload: 'Envoi',
        file: 'Fichier',
        uploadError: 'Erreur'
      },
      hu: {
        upload: 'Feltöltés',
        file: 'Fájl',
        uploadError: 'Hiba'
      },
      ja: {
        upload: 'アップロード',
        file: 'ファイル',
        uploadError: 'エラー'
      },
      ko: {
        upload: '그림 올리기',
        file: '파일',
        uploadError: '에러'
      },
      pt_br: {
        upload: 'Enviar do local',
        file: 'Arquivo',
        uploadError: 'Erro'
      },
      ru: {
        upload: 'Загрузка',
        file: 'Файл',
        uploadError: 'Ошибка'
      },
      sl: {
        upload: 'Naloži datoteko',
        file: 'Datoteka',
        uploadError: 'Napaka'
      },
      sk: {
        upload: 'Nahrať',
        file: 'Súbor',
        uploadError: 'Chyba'
      },
      tr: {
        upload: 'Yükle',
        file: 'Dosya',
        uploadError: 'Hata'
      },
      zh_cn: {
        upload: '上传',
        file: '文件',
        uploadError: '错误'
      },
      zh_tw: {
        upload: '上傳',
        file: '文件',
        uploadError: '錯誤'
      },
    },
    // jshint camelcase:true

    plugins: {
      upload: {
        init: function (trumbowyg) {
          trumbowyg.o.plugins.upload = $.extend(true, {}, defaultOptions, trumbowyg.o.plugins.upload || {});
          var btnDef = {
            fn: function () {
              trumbowyg.saveRange();

              var file,
                prefix = trumbowyg.o.prefix;

              var fields = {
                file: {
                  type: 'file',
                  required: true,
                  attributes: {
                    accept: 'image/*, video/*',

                  }
                },
                alt: {
                  label: 'description',
                  value: trumbowyg.getRangeText()
                }
              };

              if (trumbowyg.o.plugins.upload.imageWidthModalEdit) {
                fields.width = {
                  value: ''
                };
              }

              // Prevent multiple submissions while uploading
              var isUploading = false;

              var $modal = trumbowyg.openModalInsert(
                // Title
                trumbowyg.lang.upload,

                // Fields
                fields,

                // Callback
                function (values) {
                  if (isUploading) {
                    return;
                  }
                  isUploading = true;

                  var data = new FormData();
                  data.append(trumbowyg.o.plugins.upload.fileFieldName, file);

                  trumbowyg.o.plugins.upload.data.map(function (cur) {
                    data.append(cur.name, cur.value);
                  });

                  $.map(values, function (curr, key) {
                    if (key !== 'file') {
                      data.append(key, curr);
                    }
                  });

                  if ($('.' + prefix + 'progress', $modal).length === 0) {
                    $('.' + prefix + 'modal-title', $modal)
                      .after(
                        $('<div/>', {
                          'class': prefix + 'progress'
                        }).append(
                          $('<div/>', {
                            'class': prefix + 'progress-bar'
                          })
                        )
                      );
                  }
                  
                  uploadFile(file, trumbowyg).then((data) => {
                    const url = data[1];
                    const s3Resp = data[2];
                    
                    // Check if it's a video file based on MIME type, regardless of S3 response
                    const isVideoFile = file.type && file.type.startsWith('video/');
                    
                    // Override fileType if it's a video file but S3 returned "file"
                    if (isVideoFile && s3Resp.fileType === 'file') {
                      s3Resp.fileType = 'video';
                    }
                    //trumbowyg.execCmd('insertImage', url, false, true);
                    if (s3Resp.fileType === 'image' || (s3Resp.fileType === 'file' && !isVideoFile)) {
                      let image = new Image();

                      image.onload = function () {
                        const dimensions = {}
                        dimensions.width = this.width
                        dimensions.height = this.height;

                        renderImageTag(this.src, dimensions, this.alt)
                      };
                      image.alt = values.alt
                      image.src = url;
                      const renderImageTag = (_url, _dimensions, _alt) => {
                        const apiUrl = _url.substr(_url.indexOf('/api'));
                        const html = `<div class="uploaded-media" style="max-width: ${_dimensions.width}px;max-height: ${_dimensions.height}px"><img src="${apiUrl}" width="${_dimensions.width}" height="${_dimensions.height}" alt="${_alt}" /></div><p><br /></p>`
                        trumbowyg.execCmd('insertHTML', html)

                        var $img = $('img[src="' + apiUrl + '"]', trumbowyg.$box).last();
                        $img.attr('alt', values.alt);
                        if (trumbowyg.o.plugins.upload.imageWidthModalEdit && parseInt(values.width) > 0) {
                          $img.attr({
                            width: values.width
                          });
                        }

                        // Mark image as having error handlers to prevent duplicate binding
                        $img.attr('data-error-handled', 'true');

                        // Add error handling for the newly inserted image
                        $img.on('error', function() {
                          const $container = $(this).closest('.uploaded-media');
                          $container.html('<div class="image-not-uploaded" style="padding: 1rem; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Image not uploaded</div>');
                        });

                        // Add load handling for the newly inserted image
                        $img.on('load', function() {
                          $(this).addClass('img-fluid');
                        });
                        setTimeout(function () {
                          trumbowyg.closeModal();
                        }, 250);
                        trumbowyg.$c.trigger('tbwuploadsuccess', [trumbowyg, data, url]);

                      }
                    } else if (s3Resp.fileType === 'video') {
                      
                      const loadingText = (typeof t === 'function') ? t('Loading media...') : 'Loading media...';
                      const urlWithoutQuery = url.split('?')[0];
                      const fileExt = urlWithoutQuery.split('.').pop().toLowerCase();
                      const isVideo = ['mp4', 'mov', 'webm', 'ogg', 'avi', 'mkv', 'm4v'].includes(fileExt);
                      
                      const filename = url.split('/').pop().split('?')[0];
                      
                      let html;
                      if (isVideo) {
                        html = `
                          <div class="video-embed-container" 
                               data-video-url="${url}" 
                               data-video-type="${fileExt === 'mov' ? 'quicktime' : fileExt}"
                               style="max-width: 100%; margin: 1rem 0;">
                            <div class="video-loading-placeholder" style="padding: 1rem; color: #666; text-align: center;">
                              Loading video...
                            </div>
                          </div>
                          <p><br /></p>
                        `;
                        
                      } else {
                        html = `
                          <div class="video-embed-container" 
                               data-video-url="${url}" 
                               data-video-type="mp4"
                               style="max-width: 100%; margin: 1rem 0;">
                            <div class="video-loading-placeholder" style="padding: 1rem; color: #666; text-align: center;">
                              Loading video...
                            </div>
                          </div>
                          <p><br /></p>
                        `;
                      }
                      
                      try {
                        trumbowyg.execCmd('insertHTML', html);
                      } catch (err) {
                        try {
                          const simpleHtml = `
                            <div class="video-embed-container" data-video-url="${url}" style="max-width: 100%; margin: 1rem 0;">
                              <div class="video-loading-placeholder" style="padding: 1rem; color: #666; text-align: center;">
                                Loading video...
                              </div>
                            </div>
                            <p><br /></p>
                          `;
                          trumbowyg.execCmd('insertHTML', simpleHtml);
                        } catch (simpleErr) {
                          trumbowyg.execCmd('insertHTML', '<p>Failed to insert video</p>');
                        }
                      }
                      
                      // Use VideoRenderer for consistent video processing
                      setTimeout(() => {
                        const $editor = trumbowyg.$ed || trumbowyg.$ta;
                        const $newContainer = $editor.find('.video-embed-container[data-video-url="' + url + '"]').last();

                        // Only log if video debugging is enabled
                        if (window.VideoDebugLogger && window.VideoDebugLogger.enabled) {
                          console.log('🎥 WYSIWYG: Starting video processing pipeline', { url, uploadResponse: data });
                          console.log('🎥 WYSIWYG: Container search results', {
                            url,
                            containerFound: $newContainer.length > 0,
                            containerCount: $editor.find('.video-embed-container').length,
                            allContainers: $editor.find('.video-embed-container').map(function() {
                              return $(this).attr('data-video-url');
                            }).get()
                          });
                        }

                        if ($newContainer.length > 0) {
                          // Only log if video debugging is enabled
                          if (window.VideoDebugLogger && window.VideoDebugLogger.enabled) {
                            console.log('🎥 WYSIWYG: Attempting to trigger VideoRenderer', {
                              url,
                              videoRendererAvailable: !!(window.videoRenderer && window.videoRenderer.activateVideoPlayers),
                              editorType: $editor.hasClass('trumbowyg-editor') ? 'trumbowyg' : 'other'
                            });
                          }

                          if (window.videoRenderer && window.videoRenderer.activateVideoPlayers) {
                            if (window.VideoDebugLogger && window.VideoDebugLogger.enabled) {
                              console.log('🎥 WYSIWYG: Calling VideoRenderer.activateVideoPlayers');
                            }
                            setTimeout(() => {
                              window.videoRenderer.activateVideoPlayers({
                                targetContainers: [$editor],
                                staggerDelay: 100,
                                retryDelay: 200
                              });
                            }, 500);
                          } else if (window.VideoDebugLogger && window.VideoDebugLogger.enabled) {
                            console.warn('🎥 WYSIWYG: VideoRenderer not available', { url });
                          }
                        }
                      }, 100);
                      
                      try {
                        trumbowyg.closeModal();
                      } catch (err) {
                        const $modal = $('.trumbowyg-modal');
                        if ($modal.length > 0) {
                          $modal.removeClass('trumbowyg-open').hide();
                          $('.trumbowyg-modal-box').hide();
                          $('body').removeClass('trumbowyg-body-fullscreen');
                        }
                      }

                      setTimeout(function () {
                        const $modal = $('.trumbowyg-modal');
                        if ($modal.is(':visible')) {
                          try {
                            if (typeof trumbowyg.closeModal === 'function') {
                              trumbowyg.closeModal();
                            }
                            $modal.removeClass('trumbowyg-open').hide();
                            $('.trumbowyg-modal-box').hide();
                            $('body').removeClass('trumbowyg-body-fullscreen');
                          } catch (err) {
                            // Force remove as last resort
                            $modal.detach();
                            $('.trumbowyg-modal-box').detach();
                          }
                        }
                      }, 500);

                      trumbowyg.$c.trigger('tbwuploadsuccess', [trumbowyg, data, url]);

                    }
                    isUploading = false;


                  }).catch((error) => {
                    isUploading = false;
                    alert('Upload failed. Please try again.');
                  });


                }
              );

              $('input[type=file]').on('change', function (e) {
                try {
                  // If multiple files allowed, we just get the first.
                  file = e.target.files[0];
                } catch (err) {
                  // In IE8, multiple files not allowed
                  file = e.target.value;
                }
              });
            }
          };

          trumbowyg.addBtnDef('upload', btnDef);
        }
      }
    }
  });

  function addXhrProgressEvent() {
    if (!$.trumbowyg.addedXhrProgressEvent) {   // Avoid adding progress event multiple times
      var originalXhr = $.ajaxSettings.xhr;
      $.ajaxSetup({
        xhr: function () {
          var that = this,
            req = originalXhr();

          if (req && typeof req.upload === 'object' && that.progressUpload !== undefined) {
            req.upload.addEventListener('progress', function (e) {
              that.progressUpload(e);
            }, false);
          }

          return req;
        }
      });
      $.trumbowyg.addedXhrProgressEvent = true;
    }
  }


  async function saveSecureUploadAPI(params) {
    try {
      const data = await $.ajax({
        method: 'POST',
        url: `/api/admin/secure-upload/files?organisationId=${USER_SESSION.organisationId}`,
        data: params,
      });
      return data;
    } catch (err) {
      console.error(err);
      return [false];
    }
  }

  async function uploadToS3API(trumbowyg, tokenUrl, formData, xhr) {
    try {
      
      const prefix = trumbowyg.o.prefix;

      const response = await $.ajax({
        url: tokenUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr,
        progressUpload: function (e) {
                  $('.' + prefix + 'progress-bar').css('width', Math.round(e.loaded * 100 / e.total) + '%');
              },
      });
      return [true, response];
    } catch (err) {
      console.error(err);
      return [false];
    }
  }

  async function getSecureUploadTokenAPI(fileName) {
    try {
      const data = await $.ajax({
        method: 'GET',
        url: `/api/admin/secure-upload/token?fileName=${fileName}&organisationId=${USER_SESSION.organisationId}`,
      });
      return [true, data];
    } catch (err) {
      console.error(err);
      return [false];
    }
  }

  async function uploadFile(file, trumbowyg) {
    const tokenRes = await getSecureUploadTokenAPI(file.name);
    if (!tokenRes[0]) return tokenRes;
    const { key, url: uploadUrl, fields } = tokenRes[1];

    const formData = new FormData();
    Object.entries(fields).forEach(([k, value]) => {
      formData.append(k, value);
    });
    formData.append('file', file);

    const s3Resp = await uploadToS3API(trumbowyg, uploadUrl, formData);
    if (!s3Resp[0]) return s3Resp;

    const fileType = key.split('/')[0];
    const postRes = await saveSecureUploadAPI({
      key,
      name: file.name,
      size: file.size,
      type: fileType,
    });
    return [true, `/api/admin/secure-upload/private/${key}?organisationId=${USER_SESSION.organisationId}`, postRes];

  }
})(jQuery);